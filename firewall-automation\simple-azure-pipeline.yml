# Simple Azure DevOps Pipeline for Firewall Change Automation
# This pipeline analyzes Jira firewall change requests using a single script

trigger: none  # Manual trigger only

parameters:
- name: jiraIssue
  displayName: 'Jira Issue Key'
  type: string
  default: ''

- name: dryRun
  displayName: 'Dry Run (do not post comments)'
  type: boolean
  default: true

variables:
  pythonVersion: '3.11'

pool:
  vmImage: 'ubuntu-latest'

steps:
- script: |
    if [ -z "${{ parameters.jiraIssue }}" ]; then
      echo "##vso[task.logissue type=error]Jira Issue Key is required"
      exit 1
    fi
    
    if [[ ! "${{ parameters.jiraIssue }}" =~ ^[A-Z]+-[0-9]+$ ]]; then
      echo "##vso[task.logissue type=error]Invalid Jira Issue Key format. Expected format: PROJECT-123"
      exit 1
    fi
    
    echo "✅ Input validation passed"
    echo "Jira Issue: ${{ parameters.jiraIssue }}"
    echo "Dry Run: ${{ parameters.dryRun }}"
  displayName: 'Validate Parameters'

- task: UsePythonVersion@0
  inputs:
    versionSpec: '$(pythonVersion)'
    addToPath: true
    architecture: 'x64'
  displayName: 'Use Python $(pythonVersion)'

- script: |
    cd firewall-automation
    pip install -r simple_requirements.txt
  displayName: 'Install Dependencies'

- script: |
    cd firewall-automation
    python simple_firewall_analyzer.py \
      --jira-issue "${{ parameters.jiraIssue }}" \
      ${{ eq(parameters.dryRun, true) && '--dry-run' || '' }}
  env:
    JIRA_URL: $(JIRA_URL)
    JIRA_USERNAME: $(JIRA_USERNAME)
    JIRA_API_TOKEN: $(JIRA_API_TOKEN)
    TUFIN_URL: $(TUFIN_URL)
    TUFIN_USERNAME: $(TUFIN_USERNAME)
    TUFIN_PASSWORD: $(TUFIN_PASSWORD)
  displayName: 'Run Firewall Analysis'

- script: |
    echo "## 📊 Firewall Analysis Complete"
    echo "**Jira Issue:** ${{ parameters.jiraIssue }}"
    echo "**Analysis Date:** $(date)"
    echo "**Dry Run:** ${{ parameters.dryRun }}"
    
    if [ "${{ parameters.dryRun }}" = "true" ]; then
      echo "ℹ️  This was a dry run - no comments were posted to Jira"
    else
      echo "✅ Analysis results have been posted to the Jira issue"
    fi
  displayName: 'Pipeline Summary'
