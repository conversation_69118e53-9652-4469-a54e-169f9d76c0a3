# Simple Firewall Change Analyzer

A streamlined Python script for analyzing Jira firewall change requests and providing automated security recommendations.

## Overview

This simplified version consolidates all functionality into a single script (`simple_firewall_analyzer.py`) that:

1. **Retrieves Jira issues** via API
2. **Parses firewall table data** from issue descriptions
3. **Validates against security standards** using built-in rules
4. **Queries Tufin API** for existing rules (optional)
5. **Generates recommendations** based on findings
6. **Posts comments** back to <PERSON><PERSON> with results

## Quick Start

### 1. Install Dependencies
```bash
pip install -r simple_requirements.txt
```

### 2. Set Environment Variables
```bash
export JIRA_URL="https://your-jira-instance.com"
export JIRA_USERNAME="your-username"
export JIRA_API_TOKEN="your-api-token"

# Optional - for Tufin integration
export TUFIN_URL="https://your-tufin-instance.com"
export TUFIN_USERNAME="your-tufin-username"
export TUFIN_PASSWORD="your-tufin-password"
```

### 3. Run Analysis
```bash
# Dry run (no comments posted)
python simple_firewall_analyzer.py --jira-issue FW-123 --dry-run

# Live run (posts comments to Jira)
python simple_firewall_analyzer.py --jira-issue FW-123
```

## Security Standards

The analyzer checks for these risky patterns:

| Pattern | Severity | Description |
|---------|----------|-------------|
| `any`, `0.0.0.0/0`, `*` in source/destination | HIGH | Overly permissive rules |
| SSH (port 22) | MEDIUM | Secure shell access |
| RDP (port 3389) | HIGH | Remote desktop access |
| Telnet (port 23) | CRITICAL | Insecure protocol |
| FTP (port 21) | MEDIUM | File transfer protocol |
| Wide port ranges (>100 ports) | MEDIUM | Excessive port exposure |

## Expected Jira Issue Format

The script expects firewall rules in table format within the issue description:

```
| Field | Value |
|-------|-------|
| Source | 10.0.0.0/24 |
| Destination | ************* |
| Port | 80 |
| Protocol | HTTP |
| Action | allow |
```

## Testing

Run the test script to verify functionality with mock data:

```bash
python test_simple_analyzer.py
```

## Azure DevOps Integration

Use the simplified pipeline (`simple-azure-pipeline.yml`) to run from Azure DevOps:

1. **Set pipeline variables** for Jira/Tufin credentials
2. **Run pipeline** with Jira issue key parameter
3. **Review results** in pipeline output

### Pipeline Parameters:
- `jiraIssue`: Jira issue key (e.g., FW-123)
- `dryRun`: Whether to post comments (default: true)

## Files

- `simple_firewall_analyzer.py` - Main analyzer script (~350 lines)
- `simple_requirements.txt` - Python dependencies (just `requests`)
- `simple-azure-pipeline.yml` - Simplified Azure DevOps pipeline
- `test_simple_analyzer.py` - Test script with mock data
- `SIMPLE_README.md` - This documentation

## Comparison with Complex Version

| Feature | Complex Version | Simple Version |
|---------|----------------|----------------|
| **Files** | 15+ modules | 1 main script |
| **Lines of Code** | ~1000+ | ~350 |
| **Dependencies** | Conda environment | Just `requests` |
| **Configuration** | JSON files + settings | Environment variables |
| **Pipeline Stages** | 4 stages | 1 stage |
| **Error Handling** | Comprehensive logging | Basic try/catch |
| **Output** | JSON files + logs | Direct Jira comments |

## Advantages of Simple Version

✅ **Easy to understand** - Single file with clear flow  
✅ **Quick to deploy** - Minimal dependencies  
✅ **Easy to modify** - All logic in one place  
✅ **Fast execution** - No complex orchestration  
✅ **Simple debugging** - Straightforward error messages  

## Limitations

⚠️ **Basic error handling** - Less robust than complex version  
⚠️ **No detailed logging** - Uses print statements  
⚠️ **Hardcoded security rules** - No external configuration  
⚠️ **Simple parsing** - May miss complex table formats  
⚠️ **No output files** - Results only in Jira comments  

## When to Use

**Use Simple Version when:**
- You want quick setup and deployment
- The team prefers straightforward, readable code
- You don't need extensive logging or error handling
- Basic security validation is sufficient

**Use Complex Version when:**
- You need comprehensive error handling and logging
- You require detailed output files and reports
- You want configurable security standards
- You need advanced parsing capabilities
