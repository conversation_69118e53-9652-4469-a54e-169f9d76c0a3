#!/usr/bin/env python3
"""
Simple Firewall Change Analyzer
===============================
A streamlined script for analyzing Jira firewall change requests and providing
automated security recommendations.

This script consolidates all functionality into a single file for easy understanding
and maintenance. It performs the following workflow:
1. Connects to Jira API to retrieve issue data
2. Parses firewall table information from issue description
3. Validates rules against built-in security standards
4. Optionally queries Tufin API for existing rule analysis
5. Generates security recommendations based on findings
6. Posts results back to <PERSON>ra as comments

Usage:
    python simple_firewall_analyzer.py --jira-issue FW-123 [--dry-run]

Environment Variables Required:
    JIRA_URL - Your Jira instance URL (e.g., https://company.atlassian.net)
    JIRA_USERNAME - Your Jira username or email
    JIRA_API_TOKEN - Jira API token (generate from Jira settings)

    Optional (for Tufin integration):
    TUFIN_URL - Tufin SecureTrack API URL
    TUFIN_USERNAME - Tufin username
    TUFIN_PASSWORD - Tufin password

Author: Firewall Automation Team
Version: 1.0 (Simplified)
"""

# Standard library imports for core functionality
import os          # For environment variable access
import sys         # For exit codes and system operations
import re          # For regular expression pattern matching
import json        # For JSON data handling (if needed)
import argparse    # For command-line argument parsing
import requests    # For HTTP API calls to Jira and Tufin

# Type hints for better code documentation and IDE support
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class FirewallRule:
    """
    Data structure representing a single firewall rule.

    This class holds all the essential information about a firewall rule
    that we extract from Jira issue descriptions.

    Attributes:
        source (str): Source IP address, network, or 'any'
        destination (str): Destination IP address, network, or 'any'
        port (str): Port number, range, or service name
        protocol (str): Network protocol (TCP, UDP, HTTP, SSH, etc.)
        action (str): Rule action, defaults to 'allow'

    Example:
        rule = FirewallRule(
            source="10.0.0.0/24",
            destination="*************",
            port="80",
            protocol="HTTP",
            action="allow"
        )
    """
    source: str
    destination: str
    port: str
    protocol: str
    action: str = "allow"  # Default action if not specified


@dataclass
class SecurityViolation:
    """
    Data structure representing a security standard violation.

    When a firewall rule doesn't meet security standards, we create
    a SecurityViolation object to track the details.

    Attributes:
        rule_index (int): Which rule number (0-based) has the violation
        field (str): Which field has the issue (source, destination, port, etc.)
        value (str): The actual value that caused the violation
        issue (str): Description of what security issue was detected
        severity (str): How serious the violation is (CRITICAL, HIGH, MEDIUM, LOW)
        recommendation (str): Suggested fix for the violation

    Example:
        violation = SecurityViolation(
            rule_index=0,
            field="source",
            value="any",
            issue="Overly permissive source",
            severity="HIGH",
            recommendation="Restrict source to specific IP ranges"
        )
    """
    rule_index: int
    field: str
    value: str
    issue: str
    severity: str
    recommendation: str


class SimpleFirewallAnalyzer:
    """
    Main class that handles all firewall change analysis functionality.

    This class encapsulates all the logic for:
    - Connecting to external APIs (Jira, Tufin)
    - Parsing firewall rules from Jira issues
    - Validating rules against security standards
    - Generating and posting recommendations

    The class is designed to be simple and self-contained, with all
    configuration coming from environment variables.
    """

    def __init__(self):
        """
        Initialize the analyzer with configuration from environment variables.

        This constructor reads all necessary configuration from environment
        variables and sets up the security standards that will be used
        for validation.

        Environment Variables Used:
            JIRA_URL: Base URL for Jira instance
            JIRA_USERNAME: Username for Jira authentication
            JIRA_API_TOKEN: API token for Jira authentication
            TUFIN_URL: Base URL for Tufin API (optional)
            TUFIN_USERNAME: Username for Tufin authentication (optional)
            TUFIN_PASSWORD: Password for Tufin authentication (optional)
        """
        # Load Jira configuration from environment variables
        # These are required for the script to function
        self.jira_url = os.getenv('JIRA_URL')
        self.jira_username = os.getenv('JIRA_USERNAME')
        self.jira_token = os.getenv('JIRA_API_TOKEN')

        # Load Tufin configuration from environment variables
        # These are optional - script will work without Tufin integration
        self.tufin_url = os.getenv('TUFIN_URL')
        self.tufin_username = os.getenv('TUFIN_USERNAME')
        self.tufin_password = os.getenv('TUFIN_PASSWORD')

        # Define security standards as patterns to detect risky configurations
        # Each pattern has a regex to match against and a severity level
        # You can modify these patterns to add/remove/change security checks
        self.risky_patterns = {
            # Detect overly permissive source addresses
            'any_source': {
                'pattern': r'(any|0\.0\.0\.0/0|\*)',  # Matches 'any', '0.0.0.0/0', or '*'
                'severity': 'HIGH'
            },
            # Detect overly permissive destination addresses
            'any_destination': {
                'pattern': r'(any|0\.0\.0\.0/0|\*)',  # Matches 'any', '0.0.0.0/0', or '*'
                'severity': 'HIGH'
            },
            # Detect SSH protocol usage (can be risky if not properly secured)
            'ssh_protocol': {
                'pattern': r'(ssh|22)',  # Matches 'ssh' or port '22'
                'severity': 'MEDIUM'
            },
            # Detect RDP protocol usage (high risk for external access)
            'rdp_protocol': {
                'pattern': r'(rdp|3389)',  # Matches 'rdp' or port '3389'
                'severity': 'HIGH'
            },
            # Detect Telnet protocol usage (critical - unencrypted)
            'telnet_protocol': {
                'pattern': r'(telnet|23)',  # Matches 'telnet' or port '23'
                'severity': 'CRITICAL'
            },
            # Detect FTP protocol usage (medium risk - often unencrypted)
            'ftp_protocol': {
                'pattern': r'(ftp|21)',  # Matches 'ftp' or port '21'
                'severity': 'MEDIUM'
            },
            # Detect wide port ranges (can expose too many services)
            'wide_port_range': {
                'pattern': r'(\d+)-(\d+)',  # Matches patterns like '1000-2000'
                'severity': 'MEDIUM'
            }
        }

    def validate_config(self) -> bool:
        """
        Validate that all required configuration is present.

        This method checks that the minimum required environment variables
        are set for the script to function. Jira credentials are required,
        while Tufin credentials are optional.

        Returns:
            bool: True if configuration is valid, False otherwise

        Note:
            If this returns False, the script should exit as it cannot
            connect to Jira to retrieve issues.
        """
        # Check if all required Jira configuration is present
        if not all([self.jira_url, self.jira_username, self.jira_token]):
            print("❌ Missing required Jira configuration")
            print("Required environment variables:")
            print("  JIRA_URL - Your Jira instance URL")
            print("  JIRA_USERNAME - Your Jira username")
            print("  JIRA_API_TOKEN - Your Jira API token")
            return False

        print("✅ Jira configuration validated")

        # Check if Tufin configuration is present (optional)
        if all([self.tufin_url, self.tufin_username, self.tufin_password]):
            print("✅ Tufin configuration found - will include Tufin analysis")
        else:
            print("ℹ️  Tufin configuration not complete - will skip Tufin analysis")

        return True

    def get_jira_issue(self, issue_key: str) -> Optional[Dict]:
        """
        Retrieve issue data from Jira using the REST API.

        This method connects to Jira and fetches the complete issue data
        including the description field where firewall rules are expected
        to be documented.

        Args:
            issue_key (str): The Jira issue key (e.g., 'FW-123', 'PROJ-456')

        Returns:
            Optional[Dict]: The complete Jira issue data as a dictionary,
                          or None if the request failed

        API Details:
            - Uses Jira REST API v2
            - Authenticates with username and API token
            - Fetches all issue fields including description
            - Has 30-second timeout to prevent hanging
        """
        print(f"📥 Retrieving Jira issue: {issue_key}")

        # Construct the Jira REST API URL for the specific issue
        url = f"{self.jira_url}/rest/api/2/issue/{issue_key}"

        # Set up HTTP Basic Authentication using username and API token
        auth = (self.jira_username, self.jira_token)

        try:
            # Make the HTTP GET request to Jira
            response = requests.get(url, auth=auth, timeout=30)

            # Raise an exception if the HTTP request failed (4xx or 5xx status)
            response.raise_for_status()

            # Parse the JSON response and return it
            issue_data = response.json()
            print(f"✅ Successfully retrieved issue: {issue_data.get('key', 'Unknown')}")
            return issue_data

        except requests.RequestException as e:
            # Handle any HTTP-related errors (network, authentication, etc.)
            print(f"❌ Failed to retrieve Jira issue: {e}")
            print(f"   Check that the issue key '{issue_key}' exists and you have access to it")
            return None

    def parse_firewall_table(self, issue_data: Dict) -> List[FirewallRule]:
        """
        Parse firewall rules from the Jira issue description.

        This method looks for firewall rule information in the issue description
        and extracts it into structured FirewallRule objects. It expects the
        rules to be formatted as tables with Field|Value pairs.

        Expected Format in Jira Description:
            | Field | Value |
            |-------|-------|
            | Source | 10.0.0.0/24 |
            | Destination | ************* |
            | Port | 80 |
            | Protocol | HTTP |
            | Action | allow |

        Args:
            issue_data (Dict): The complete Jira issue data from the API

        Returns:
            List[FirewallRule]: List of parsed firewall rules, empty if none found

        Parsing Logic:
            1. Extract description field from issue data
            2. Use regex to find Field|Value patterns
            3. Group consecutive field/value pairs into rules
            4. Create FirewallRule objects from the grouped data
        """
        print("🔍 Parsing firewall rules from issue description")

        # Extract the description field from the Jira issue data
        description = issue_data.get('fields', {}).get('description', '')
        if not description:
            print("⚠️  No description found in issue")
            return []

        print(f"📄 Description length: {len(description)} characters")

        rules = []

        # Define regex pattern to match table rows with Field|Value format
        # This pattern looks for:
        # - Optional pipe character at start: \|?
        # - Whitespace: \s*
        # - Field name (Source, Destination, etc.): (Source|Destination|Port|Protocol|Action)
        # - Whitespace and pipe separator: \s*\|?\s*
        # - Value (everything until next pipe or newline): ([^\|\n]+)
        table_pattern = r'\|?\s*(Source|Destination|Port|Protocol|Action)\s*\|?\s*([^\|\n]+)'

        # Find all matches in the description (case-insensitive)
        matches = re.findall(table_pattern, description, re.IGNORECASE)

        if not matches:
            print("⚠️  No firewall table found in description")
            print("   Expected format: | Field | Value |")
            return []

        print(f"🔍 Found {len(matches)} field/value pairs")

        # Group the field/value pairs into complete firewall rules
        current_rule = {}
        rule_count = 0

        for field, value in matches:
            # Clean up the field name and value
            field = field.strip().lower()
            value = value.strip()

            # Only process recognized firewall rule fields
            if field in ['source', 'destination', 'port', 'protocol', 'action']:
                current_rule[field] = value
                print(f"   Found {field}: {value}")

                # Check if we have enough fields to create a complete rule
                # Minimum required: source, destination, port, protocol
                if len(current_rule) >= 4:
                    rule_count += 1

                    # Create a new FirewallRule object
                    rule = FirewallRule(
                        source=current_rule.get('source', ''),
                        destination=current_rule.get('destination', ''),
                        port=current_rule.get('port', ''),
                        protocol=current_rule.get('protocol', ''),
                        action=current_rule.get('action', 'allow')  # Default to 'allow'
                    )

                    rules.append(rule)
                    print(f"✅ Created rule {rule_count}: {rule.source} -> {rule.destination}:{rule.port}")

                    # Reset for next rule
                    current_rule = {}

        print(f"✅ Successfully parsed {len(rules)} firewall rules")
        return rules

    def validate_security_standards(self, rules: List[FirewallRule]) -> List[SecurityViolation]:
        """
        Validate firewall rules against built-in security standards.

        This method checks each firewall rule against a set of predefined
        security patterns to identify potential risks. It examines source,
        destination, port, and protocol fields for known risky configurations.

        Args:
            rules (List[FirewallRule]): List of firewall rules to validate

        Returns:
            List[SecurityViolation]: List of security violations found

        Security Checks Performed:
            1. Overly permissive sources (any, 0.0.0.0/0, *)
            2. Overly permissive destinations (any, 0.0.0.0/0, *)
            3. Risky protocols (SSH, RDP, Telnet, FTP)
            4. Wide port ranges (>100 ports)

        Customization:
            To modify security checks, edit the self.risky_patterns dictionary
            in the __init__ method. Each pattern has a regex and severity level.
        """
        print("🔒 Validating against security standards")
        print(f"📋 Checking {len(rules)} rules against {len(self.risky_patterns)} security patterns")

        violations = []

        # Check each rule against all security patterns
        for i, rule in enumerate(rules):
            print(f"   Checking rule {i+1}: {rule.source} -> {rule.destination}:{rule.port}")

            # Iterate through each security check pattern
            for check_name, check_config in self.risky_patterns.items():
                pattern = check_config['pattern']
                severity = check_config['severity']

                # Check SOURCE field for risky patterns
                if 'source' in check_name:
                    if re.search(pattern, rule.source, re.IGNORECASE):
                        violation = SecurityViolation(
                            rule_index=i,
                            field='source',
                            value=rule.source,
                            issue=f"Risky source pattern detected: {check_name.replace('_', ' ')}",
                            severity=severity,
                            recommendation="Consider restricting source to specific IP ranges or networks"
                        )
                        violations.append(violation)
                        print(f"     ⚠️  {severity} violation: {violation.issue}")

                # Check DESTINATION field for risky patterns
                elif 'destination' in check_name:
                    if re.search(pattern, rule.destination, re.IGNORECASE):
                        violation = SecurityViolation(
                            rule_index=i,
                            field='destination',
                            value=rule.destination,
                            issue=f"Risky destination pattern detected: {check_name.replace('_', ' ')}",
                            severity=severity,
                            recommendation="Consider restricting destination to specific IP ranges or networks"
                        )
                        violations.append(violation)
                        print(f"     ⚠️  {severity} violation: {violation.issue}")

                # Check PROTOCOL/PORT fields for risky patterns
                elif 'protocol' in check_name:
                    # Combine protocol and port for checking
                    protocol_port_string = f"{rule.protocol} {rule.port}"
                    if re.search(pattern, protocol_port_string, re.IGNORECASE):
                        violation = SecurityViolation(
                            rule_index=i,
                            field='protocol/port',
                            value=f"{rule.protocol}:{rule.port}",
                            issue=f"Risky protocol/port detected: {check_name.replace('_', ' ')}",
                            severity=severity,
                            recommendation="Consider using more secure protocols or restricting access"
                        )
                        violations.append(violation)
                        print(f"     ⚠️  {severity} violation: {violation.issue}")

                # Check for WIDE PORT RANGES (special case)
                elif 'port_range' in check_name:
                    match = re.search(pattern, rule.port)
                    if match:
                        # Extract start and end port numbers
                        start_port, end_port = match.groups()
                        try:
                            port_range_size = int(end_port) - int(start_port)

                            # Flag ranges wider than 100 ports as risky
                            if port_range_size > 100:
                                violation = SecurityViolation(
                                    rule_index=i,
                                    field='port',
                                    value=rule.port,
                                    issue=f"Wide port range detected: {port_range_size + 1} ports",
                                    severity=severity,
                                    recommendation="Consider narrowing port range to specific required ports"
                                )
                                violations.append(violation)
                                print(f"     ⚠️  {severity} violation: {violation.issue}")
                        except ValueError:
                            # Handle case where port numbers aren't valid integers
                            print(f"     ⚠️  Could not parse port range: {rule.port}")

        print(f"🔍 Security validation complete: found {len(violations)} violations")

        # Print summary by severity
        severity_counts = {}
        for violation in violations:
            severity_counts[violation.severity] = severity_counts.get(violation.severity, 0) + 1

        if severity_counts:
            print("📊 Violations by severity:")
            for severity, count in sorted(severity_counts.items()):
                print(f"   {severity}: {count}")

        return violations

    def query_tufin_rules(self, rules: List[FirewallRule]) -> Dict[str, Any]:
        """
        Query Tufin SecureTrack API for existing firewall rules and analysis.

        This method connects to Tufin SecureTrack to check for existing rules
        that might conflict with or relate to the requested firewall changes.
        It provides insights into the current firewall configuration.

        Args:
            rules (List[FirewallRule]): List of firewall rules to analyze

        Returns:
            Dict[str, Any]: Dictionary with analysis results for each rule

        Tufin Integration Notes:
            - This is a simplified implementation for demonstration
            - In a real environment, you would need to:
              1. Authenticate with Tufin API
              2. Query specific firewall devices
              3. Check for rule conflicts and overlaps
              4. Analyze policy compliance

        Customization:
            To implement actual Tufin integration:
            1. Replace mock responses with real API calls
            2. Add proper authentication handling
            3. Implement specific Tufin API endpoints
            4. Add error handling for Tufin-specific errors
        """
        print("🔍 Querying Tufin for existing rules and analysis")

        # Check if Tufin configuration is available
        if not all([self.tufin_url, self.tufin_username, self.tufin_password]):
            print("⚠️  Tufin configuration not provided, skipping Tufin analysis")
            print("   To enable Tufin integration, set these environment variables:")
            print("   - TUFIN_URL")
            print("   - TUFIN_USERNAME")
            print("   - TUFIN_PASSWORD")
            return {}

        print(f"🔗 Connecting to Tufin at: {self.tufin_url}")
        print(f"👤 Using username: {self.tufin_username}")

        tufin_results = {}

        # Analyze each firewall rule
        for i, rule in enumerate(rules):
            rule_key = f"rule_{i+1}"
            print(f"  🔍 Analyzing rule {i+1}: {rule.source} -> {rule.destination}:{rule.port}")

            try:
                # NOTE: This is a simplified/mock implementation
                # In a real environment, you would make actual API calls to Tufin here

                # Example of what real Tufin API calls might look like:
                # 1. Search for existing rules with similar source/destination
                # 2. Check for policy violations
                # 3. Analyze rule conflicts
                # 4. Get recommendations for rule placement

                # For now, we provide mock responses that demonstrate the structure
                tufin_results[rule_key] = {
                    "existing_rules": [
                        # Mock existing rules that might be related
                        {
                            "rule_id": f"existing_rule_{i+1}",
                            "source": "10.0.0.0/8",
                            "destination": rule.destination,
                            "port": "any",
                            "action": "allow",
                            "device": "firewall-01"
                        }
                    ],
                    "conflicts": [
                        # Mock potential conflicts
                        {
                            "type": "overlapping_rule",
                            "severity": "medium",
                            "message": f"Existing rule may overlap with requested rule for {rule.destination}",
                            "recommendation": "Review existing rules before implementing"
                        }
                    ],
                    "recommendations": [
                        f"Consider creating new rule for {rule.source} -> {rule.destination}:{rule.port}",
                        f"Verify that {rule.destination} is reachable from {rule.source}",
                        "Consider implementing rule with logging enabled for monitoring"
                    ],
                    "policy_compliance": {
                        "compliant": True,
                        "violations": []
                    }
                }

                print(f"    ✅ Tufin analysis completed for rule {i+1}")

            except Exception as e:
                # Handle any errors during Tufin analysis
                print(f"    ❌ Failed to query Tufin for rule {i+1}: {e}")
                tufin_results[rule_key] = {
                    "error": str(e),
                    "recommendations": ["Manual review required due to Tufin query failure"]
                }

        print(f"✅ Tufin analysis complete for {len(rules)} rules")
        return tufin_results

    def generate_recommendations(self, violations: List[SecurityViolation], tufin_results: Dict) -> List[str]:
        """
        Generate comprehensive security recommendations based on analysis results.

        This method combines findings from security validation and Tufin analysis
        to create a formatted list of recommendations that will be posted to Jira.

        Args:
            violations (List[SecurityViolation]): Security violations found during validation
            tufin_results (Dict): Results from Tufin API analysis

        Returns:
            List[str]: Formatted recommendation strings ready for Jira comment

        Recommendation Structure:
            1. Security violations (if any) with severity indicators
            2. Tufin analysis results (if available)
            3. General recommendations or "all clear" message

        Customization:
            - Modify severity_emoji dict to change visual indicators
            - Add additional recommendation logic based on your needs
            - Customize formatting for different Jira markdown styles
        """
        print("📝 Generating security recommendations")
        print(f"📊 Processing {len(violations)} violations and {len(tufin_results)} Tufin results")

        recommendations = []

        # SECTION 1: Security Violation Recommendations
        if violations:
            recommendations.append("🔒 **Security Violations Found:**")
            recommendations.append("")  # Add blank line for readability

            # Group violations by severity for better organization
            violations_by_severity = {}
            for violation in violations:
                severity = violation.severity
                if severity not in violations_by_severity:
                    violations_by_severity[severity] = []
                violations_by_severity[severity].append(violation)

            # Define emoji mapping for different severity levels
            severity_emoji = {
                'CRITICAL': '🚨',  # Red alert for critical issues
                'HIGH': '⚠️',      # Warning for high-priority issues
                'MEDIUM': '⚡',    # Lightning for medium-priority issues
                'LOW': 'ℹ️'        # Info icon for low-priority issues
            }

            # Process violations in order of severity (most severe first)
            severity_order = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']
            for severity in severity_order:
                if severity in violations_by_severity:
                    for violation in violations_by_severity[severity]:
                        emoji = severity_emoji.get(violation.severity, 'ℹ️')

                        # Format the violation with rule number and details
                        recommendations.append(
                            f"{emoji} **Rule {violation.rule_index + 1}** - {violation.field}: {violation.issue}"
                        )
                        recommendations.append(f"   *Recommendation:* {violation.recommendation}")
                        recommendations.append("")  # Blank line between violations

        # SECTION 2: Tufin Analysis Recommendations
        if tufin_results:
            recommendations.append("🔍 **Tufin Analysis Results:**")
            recommendations.append("")

            for rule_key, result in tufin_results.items():
                rule_number = rule_key.replace('rule_', '')

                # Handle errors in Tufin analysis
                if 'error' in result:
                    recommendations.append(f"⚠️ **Rule {rule_number}**: Tufin analysis failed - {result['error']}")
                    if 'recommendations' in result:
                        for rec in result['recommendations']:
                            recommendations.append(f"   • {rec}")
                    recommendations.append("")
                    continue

                # Process successful Tufin analysis
                recommendations.append(f"📋 **Rule {rule_number} Analysis:**")

                # Add existing rules information
                if 'existing_rules' in result and result['existing_rules']:
                    recommendations.append(f"   *Existing Rules:* {len(result['existing_rules'])} related rules found")

                # Add conflict information
                if 'conflicts' in result and result['conflicts']:
                    for conflict in result['conflicts']:
                        recommendations.append(f"   ⚠️ *Conflict:* {conflict.get('message', 'Unknown conflict')}")

                # Add Tufin recommendations
                if 'recommendations' in result:
                    for rec in result['recommendations']:
                        recommendations.append(f"   • {rec}")

                recommendations.append("")  # Blank line between rules

        # SECTION 3: General Recommendations or All-Clear Message
        if not violations and not tufin_results:
            recommendations.append("✅ **Analysis Complete - No Issues Found**")
            recommendations.append("")
            recommendations.append("The firewall change request appears to comply with security standards.")
            recommendations.append("No security violations or conflicts were detected.")
        elif not violations and tufin_results:
            recommendations.append("✅ **Security Standards Compliance**")
            recommendations.append("")
            recommendations.append("No security standard violations detected. Review Tufin analysis above for implementation guidance.")

        # Add footer with analysis metadata
        recommendations.append("")
        recommendations.append("---")
        recommendations.append("*This analysis was generated automatically by the Firewall Security Analyzer*")

        print(f"✅ Generated {len(recommendations)} recommendation lines")
        return recommendations

    def post_jira_comment(self, issue_key: str, recommendations: List[str]) -> bool:
        """
        Post security recommendations as a comment to the Jira issue.

        This method takes the generated recommendations and posts them as a
        formatted comment on the original Jira issue. This provides immediate
        feedback to the requestor about security concerns.

        Args:
            issue_key (str): The Jira issue key to comment on
            recommendations (List[str]): List of recommendation strings to post

        Returns:
            bool: True if comment was posted successfully, False otherwise

        Comment Format:
            - Header with analysis title
            - All recommendations joined with newlines
            - Footer with timestamp and automation notice

        API Details:
            - Uses Jira REST API v2 comment endpoint
            - Authenticates with username and API token
            - Sends comment as JSON payload
            - Has 30-second timeout

        Error Handling:
            - Catches all HTTP-related errors
            - Logs specific error messages
            - Returns False on any failure
        """
        print(f"💬 Posting recommendations to Jira issue: {issue_key}")
        print(f"📝 Comment will contain {len(recommendations)} lines")

        # Build the comment body with header and footer
        comment_lines = [
            "🔒 **Automated Firewall Security Analysis**",
            "",  # Blank line for readability
        ]

        # Add all recommendations
        comment_lines.extend(recommendations)

        # Add footer with timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        comment_lines.extend([
            "",
            f"_Analysis generated automatically on {timestamp}_"
        ])

        # Join all lines into a single comment body
        comment_body = "\n".join(comment_lines)

        # Construct the Jira comment API URL
        url = f"{self.jira_url}/rest/api/2/issue/{issue_key}/comment"

        # Set up authentication
        auth = (self.jira_username, self.jira_token)

        # Prepare the JSON payload
        payload = {"body": comment_body}

        try:
            print(f"🌐 Making API call to: {url}")

            # Make the HTTP POST request to add the comment
            response = requests.post(url, auth=auth, json=payload, timeout=30)

            # Check if the request was successful
            response.raise_for_status()

            # Parse response to get comment details
            comment_data = response.json()
            comment_id = comment_data.get('id', 'unknown')

            print(f"✅ Successfully posted comment to Jira (Comment ID: {comment_id})")
            print(f"📄 Comment length: {len(comment_body)} characters")
            return True

        except requests.RequestException as e:
            # Handle HTTP errors, authentication failures, network issues, etc.
            print(f"❌ Failed to post comment to Jira: {e}")
            print(f"   Issue: {issue_key}")
            print(f"   URL: {url}")
            print(f"   Check your Jira credentials and permissions")
            return False

    def analyze(self, issue_key: str, dry_run: bool = False) -> bool:
        """
        Execute the complete firewall analysis workflow.

        This is the main orchestration method that coordinates all the analysis
        steps from retrieving the Jira issue to posting recommendations back.

        Workflow Steps:
            1. Retrieve Jira issue data via API
            2. Parse firewall rules from issue description
            3. Validate rules against security standards
            4. Query Tufin for existing rule analysis (optional)
            5. Generate comprehensive recommendations
            6. Display results to console
            7. Post recommendations to Jira (unless dry run)

        Args:
            issue_key (str): Jira issue key to analyze (e.g., 'FW-123')
            dry_run (bool): If True, don't post comments to Jira (default: False)

        Returns:
            bool: True if analysis completed successfully, False if any step failed

        Error Handling:
            - Each step is validated before proceeding to the next
            - Failures are logged with specific error messages
            - Returns False immediately on critical failures
            - Continues with warnings for non-critical issues

        Usage Examples:
            analyzer.analyze('FW-123')  # Full analysis with Jira comment
            analyzer.analyze('FW-123', dry_run=True)  # Analysis only, no comment
        """
        print(f"🚀 Starting firewall analysis for {issue_key}")
        print(f"🔧 Mode: {'DRY RUN' if dry_run else 'LIVE RUN'}")
        print("=" * 60)

        # STEP 1: Retrieve Jira Issue Data
        print("\n📋 STEP 1: Retrieving Jira Issue")
        print("-" * 30)
        issue_data = self.get_jira_issue(issue_key)
        if not issue_data:
            print("❌ CRITICAL: Cannot proceed without Jira issue data")
            return False

        # STEP 2: Parse Firewall Rules from Issue Description
        print("\n🔍 STEP 2: Parsing Firewall Rules")
        print("-" * 30)
        rules = self.parse_firewall_table(issue_data)
        if not rules:
            print("❌ CRITICAL: No firewall rules found to analyze")
            print("   Check that the issue description contains a properly formatted table")
            return False

        print(f"✅ Successfully parsed {len(rules)} firewall rules")

        # STEP 3: Validate Against Security Standards
        print("\n🔒 STEP 3: Security Standards Validation")
        print("-" * 30)
        violations = self.validate_security_standards(rules)

        if violations:
            print(f"⚠️  Found {len(violations)} security violations")
        else:
            print("✅ No security violations detected")

        # STEP 4: Query Tufin for Existing Rules (Optional)
        print("\n🔍 STEP 4: Tufin Analysis")
        print("-" * 30)
        tufin_results = self.query_tufin_rules(rules)

        if tufin_results:
            print(f"✅ Tufin analysis completed for {len(tufin_results)} rules")
        else:
            print("ℹ️  Tufin analysis skipped (configuration not available)")

        # STEP 5: Generate Comprehensive Recommendations
        print("\n📝 STEP 5: Generating Recommendations")
        print("-" * 30)
        recommendations = self.generate_recommendations(violations, tufin_results)
        print(f"✅ Generated {len(recommendations)} recommendation lines")

        # STEP 6: Display Results to Console
        print("\n" + "=" * 60)
        print("📊 ANALYSIS RESULTS")
        print("=" * 60)
        for rec in recommendations:
            print(rec)
        print("=" * 60)

        # STEP 7: Post Results to Jira (Unless Dry Run)
        print("\n💬 STEP 7: Posting Results")
        print("-" * 30)
        if not dry_run:
            print("📤 Posting recommendations to Jira...")
            success = self.post_jira_comment(issue_key, recommendations)
            if not success:
                print("❌ CRITICAL: Failed to post comment to Jira")
                return False
            print("✅ Successfully posted recommendations to Jira")
        else:
            print("🔍 DRY RUN: Skipping Jira comment posting")
            print("   In live mode, recommendations would be posted as a comment")

        # FINAL SUMMARY
        print("\n" + "=" * 60)
        print("🎉 ANALYSIS COMPLETE")
        print("=" * 60)
        print(f"Issue: {issue_key}")
        print(f"Rules Analyzed: {len(rules)}")
        print(f"Violations Found: {len(violations)}")
        print(f"Tufin Results: {len(tufin_results)}")
        print(f"Mode: {'DRY RUN' if dry_run else 'LIVE RUN'}")
        print("✅ Analysis completed successfully")

        return True


def main():
    """
    Main entry point for the command-line interface.

    This function handles:
    - Command-line argument parsing
    - Analyzer initialization
    - Configuration validation
    - Workflow execution
    - Exit code management

    Command-Line Arguments:
        --jira-issue: Required. The Jira issue key to analyze (e.g., FW-123)
        --dry-run: Optional. Run analysis without posting comments to Jira

    Exit Codes:
        0: Success - Analysis completed without errors
        1: Failure - Configuration invalid or analysis failed

    Environment Variables Required:
        JIRA_URL: Your Jira instance URL
        JIRA_USERNAME: Your Jira username
        JIRA_API_TOKEN: Your Jira API token

    Environment Variables Optional:
        TUFIN_URL: Tufin SecureTrack URL
        TUFIN_USERNAME: Tufin username
        TUFIN_PASSWORD: Tufin password

    Usage Examples:
        python simple_firewall_analyzer.py --jira-issue FW-123
        python simple_firewall_analyzer.py --jira-issue PROJ-456 --dry-run
    """
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(
        description='Simple Firewall Change Analyzer - Automated security analysis for Jira firewall requests',
        epilog='''
Examples:
  %(prog)s --jira-issue FW-123
  %(prog)s --jira-issue PROJ-456 --dry-run

Environment Variables:
  JIRA_URL          Jira instance URL (required)
  JIRA_USERNAME     Jira username (required)
  JIRA_API_TOKEN    Jira API token (required)
  TUFIN_URL         Tufin API URL (optional)
  TUFIN_USERNAME    Tufin username (optional)
  TUFIN_PASSWORD    Tufin password (optional)
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # Define command-line arguments
    parser.add_argument(
        '--jira-issue',
        required=True,
        help='Jira issue key to analyze (e.g., FW-123, PROJ-456)',
        metavar='ISSUE_KEY'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Run analysis without posting comments to Jira (useful for testing)'
    )

    # Parse command-line arguments
    args = parser.parse_args()

    print("🔒 Simple Firewall Change Analyzer")
    print("=" * 50)
    print(f"Issue Key: {args.jira_issue}")
    print(f"Mode: {'DRY RUN' if args.dry_run else 'LIVE RUN'}")
    print("=" * 50)

    try:
        # Initialize the analyzer
        print("🔧 Initializing analyzer...")
        analyzer = SimpleFirewallAnalyzer()

        # Validate configuration before proceeding
        print("✅ Validating configuration...")
        if not analyzer.validate_config():
            print("\n❌ Configuration validation failed")
            print("Please check your environment variables and try again")
            sys.exit(1)

        # Run the complete analysis workflow
        print("🚀 Starting analysis workflow...")
        success = analyzer.analyze(args.jira_issue, args.dry_run)

        # Handle results and set appropriate exit code
        if success:
            print(f"\n🎉 Analysis completed successfully for {args.jira_issue}")
            if args.dry_run:
                print("💡 This was a dry run - no changes were made to Jira")
            else:
                print("✅ Recommendations have been posted to the Jira issue")
            sys.exit(0)
        else:
            print(f"\n❌ Analysis failed for {args.jira_issue}")
            print("Check the error messages above for details")
            sys.exit(1)

    except KeyboardInterrupt:
        # Handle user interruption (Ctrl+C)
        print("\n⏹️  Analysis interrupted by user")
        sys.exit(130)  # Standard exit code for SIGINT

    except Exception as e:
        # Handle any unexpected errors
        print(f"\n💥 Unexpected error occurred: {e}")
        print("This may indicate a bug in the script or an environmental issue")
        sys.exit(1)


# Entry point when script is run directly
if __name__ == '__main__':
    main()
