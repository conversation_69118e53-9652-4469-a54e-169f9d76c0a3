#!/usr/bin/env python3
"""
Simple Firewall Change Analyzer
A streamlined script for analyzing Jira firewall change requests.

Usage:
    python simple_firewall_analyzer.py --jira-issue FW-123 [--dry-run]

Environment Variables Required:
    JIRA_URL - Jira instance URL
    JIRA_USERNAME - Jira username
    JIRA_API_TOKEN - Jira API token
    TUFIN_URL - Tufin API URL (optional)
    TUFIN_USERNAME - Tufin username (optional)
    TUFIN_PASSWORD - Tufin password (optional)
"""

import os
import sys
import re
import json
import argparse
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class FirewallRule:
    """Simple firewall rule representation."""
    source: str
    destination: str
    port: str
    protocol: str
    action: str = "allow"


@dataclass
class SecurityViolation:
    """Security standard violation."""
    rule_index: int
    field: str
    value: str
    issue: str
    severity: str
    recommendation: str


class SimpleFirewallAnalyzer:
    """Simplified firewall change analyzer."""

    def __init__(self):
        self.jira_url = os.getenv('JIRA_URL')
        self.jira_username = os.getenv('JIRA_USERNAME')
        self.jira_token = os.getenv('JIRA_API_TOKEN')
        self.tufin_url = os.getenv('TUFIN_URL')
        self.tufin_username = os.getenv('TUFIN_USERNAME')
        self.tufin_password = os.getenv('TUFIN_PASSWORD')

        # Security standards (simplified)
        self.risky_patterns = {
            'any_source': {'pattern': r'(any|0\.0\.0\.0/0|\*)', 'severity': 'HIGH'},
            'any_destination': {'pattern': r'(any|0\.0\.0\.0/0|\*)', 'severity': 'HIGH'},
            'ssh_protocol': {'pattern': r'(ssh|22)', 'severity': 'MEDIUM'},
            'rdp_protocol': {'pattern': r'(rdp|3389)', 'severity': 'HIGH'},
            'telnet_protocol': {'pattern': r'(telnet|23)', 'severity': 'CRITICAL'},
            'ftp_protocol': {'pattern': r'(ftp|21)', 'severity': 'MEDIUM'},
            'wide_port_range': {'pattern': r'(\d+)-(\d+)', 'severity': 'MEDIUM'}
        }

    def validate_config(self) -> bool:
        """Validate required configuration."""
        if not all([self.jira_url, self.jira_username, self.jira_token]):
            print("❌ Missing required Jira configuration")
            print("Required: JIRA_URL, JIRA_USERNAME, JIRA_API_TOKEN")
            return False
        return True

    def get_jira_issue(self, issue_key: str) -> Optional[Dict]:
        """Retrieve Jira issue data."""
        print(f"📥 Retrieving Jira issue: {issue_key}")

        url = f"{self.jira_url}/rest/api/2/issue/{issue_key}"
        auth = (self.jira_username, self.jira_token)

        try:
            response = requests.get(url, auth=auth, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"❌ Failed to retrieve Jira issue: {e}")
            return None

    def parse_firewall_table(self, issue_data: Dict) -> List[FirewallRule]:
        """Parse firewall rules from issue description."""
        print("🔍 Parsing firewall rules from issue description")

        description = issue_data.get('fields', {}).get('description', '')
        if not description:
            print("⚠️  No description found in issue")
            return []

        rules = []

        # Look for table patterns in description
        # Simple regex to find Field|Value patterns
        table_pattern = r'\|?\s*(Source|Destination|Port|Protocol|Action)\s*\|?\s*([^\|\n]+)'
        matches = re.findall(table_pattern, description, re.IGNORECASE)

        if not matches:
            print("⚠️  No firewall table found in description")
            return []

        # Group matches into rules
        current_rule = {}
        for field, value in matches:
            field = field.strip().lower()
            value = value.strip()

            if field in ['source', 'destination', 'port', 'protocol', 'action']:
                current_rule[field] = value

                # If we have enough fields, create a rule
                if len(current_rule) >= 4:  # source, dest, port, protocol minimum
                    rules.append(FirewallRule(
                        source=current_rule.get('source', ''),
                        destination=current_rule.get('destination', ''),
                        port=current_rule.get('port', ''),
                        protocol=current_rule.get('protocol', ''),
                        action=current_rule.get('action', 'allow')
                    ))
                    current_rule = {}

        print(f"✅ Found {len(rules)} firewall rules")
        return rules

    def validate_security_standards(self, rules: List[FirewallRule]) -> List[SecurityViolation]:
        """Validate rules against security standards."""
        print("🔒 Validating against security standards")

        violations = []

        for i, rule in enumerate(rules):
            # Check source
            for check_name, check_config in self.risky_patterns.items():
                if 'source' in check_name and re.search(check_config['pattern'], rule.source, re.IGNORECASE):
                    violations.append(SecurityViolation(
                        rule_index=i,
                        field='source',
                        value=rule.source,
                        issue=f"Risky source pattern detected: {check_name}",
                        severity=check_config['severity'],
                        recommendation="Consider restricting source to specific IP ranges"
                    ))

                # Check destination
                if 'destination' in check_name and re.search(check_config['pattern'], rule.destination, re.IGNORECASE):
                    violations.append(SecurityViolation(
                        rule_index=i,
                        field='destination',
                        value=rule.destination,
                        issue=f"Risky destination pattern detected: {check_name}",
                        severity=check_config['severity'],
                        recommendation="Consider restricting destination to specific IP ranges"
                    ))

                # Check protocol/port
                if 'protocol' in check_name and re.search(check_config['pattern'], f"{rule.protocol} {rule.port}", re.IGNORECASE):
                    violations.append(SecurityViolation(
                        rule_index=i,
                        field='protocol/port',
                        value=f"{rule.protocol}:{rule.port}",
                        issue=f"Risky protocol/port detected: {check_name}",
                        severity=check_config['severity'],
                        recommendation="Consider using more secure protocols or restricting access"
                    ))

                # Check port ranges
                if 'port_range' in check_name:
                    match = re.search(check_config['pattern'], rule.port)
                    if match:
                        start_port, end_port = match.groups()
                        if int(end_port) - int(start_port) > 100:
                            violations.append(SecurityViolation(
                                rule_index=i,
                                field='port',
                                value=rule.port,
                                issue="Wide port range detected",
                                severity=check_config['severity'],
                                recommendation="Consider narrowing port range to specific required ports"
                            ))

        print(f"🔍 Found {len(violations)} security violations")
        return violations

    def query_tufin_rules(self, rules: List[FirewallRule]) -> Dict[str, Any]:
        """Query Tufin for existing rules (simplified)."""
        print("🔍 Querying Tufin for existing rules")

        if not all([self.tufin_url, self.tufin_username, self.tufin_password]):
            print("⚠️  Tufin configuration not provided, skipping Tufin analysis")
            return {}

        tufin_results = {}

        for i, rule in enumerate(rules):
            try:
                # Simplified Tufin query - this would need to be adapted to actual Tufin API
                print(f"  Checking rule {i+1}: {rule.source} -> {rule.destination}:{rule.port}")

                # Mock Tufin response for demonstration
                tufin_results[f"rule_{i+1}"] = {
                    "existing_rules": [],
                    "conflicts": [],
                    "recommendations": [f"Consider creating new rule for {rule.source} -> {rule.destination}:{rule.port}"]
                }

            except Exception as e:
                print(f"⚠️  Failed to query Tufin for rule {i+1}: {e}")
                tufin_results[f"rule_{i+1}"] = {"error": str(e)}

        return tufin_results

    def generate_recommendations(self, violations: List[SecurityViolation], tufin_results: Dict) -> List[str]:
        """Generate security recommendations."""
        print("📝 Generating recommendations")

        recommendations = []

        # Security violation recommendations
        if violations:
            recommendations.append("🔒 **Security Violations Found:**")

            for violation in violations:
                severity_emoji = {
                    'CRITICAL': '🚨',
                    'HIGH': '⚠️',
                    'MEDIUM': '⚡',
                    'LOW': 'ℹ️'
                }.get(violation.severity, 'ℹ️')

                recommendations.append(
                    f"{severity_emoji} Rule {violation.rule_index + 1} - {violation.field}: {violation.issue}"
                )
                recommendations.append(f"   Recommendation: {violation.recommendation}")

        # Tufin recommendations
        if tufin_results:
            recommendations.append("\n🔍 **Tufin Analysis:**")
            for rule_key, result in tufin_results.items():
                if 'recommendations' in result:
                    for rec in result['recommendations']:
                        recommendations.append(f"• {rec}")

        # General recommendations
        if not violations and not tufin_results:
            recommendations.append("✅ No security issues detected in the firewall change request.")

        return recommendations

    def post_jira_comment(self, issue_key: str, recommendations: List[str]) -> bool:
        """Post recommendations as comment to Jira issue."""
        print(f"💬 Posting recommendations to Jira issue: {issue_key}")

        comment_body = "🔒 **Automated Firewall Security Analysis**\n\n"
        comment_body += "\n".join(recommendations)
        comment_body += f"\n\n_Analysis generated automatically_"

        url = f"{self.jira_url}/rest/api/2/issue/{issue_key}/comment"
        auth = (self.jira_username, self.jira_token)

        payload = {"body": comment_body}

        try:
            response = requests.post(url, auth=auth, json=payload, timeout=30)
            response.raise_for_status()
            print("✅ Successfully posted comment to Jira")
            return True
        except requests.RequestException as e:
            print(f"❌ Failed to post comment to Jira: {e}")
            return False

    def analyze(self, issue_key: str, dry_run: bool = False) -> bool:
        """Main analysis workflow."""
        print(f"🚀 Starting firewall analysis for {issue_key}")

        # Step 1: Get Jira issue
        issue_data = self.get_jira_issue(issue_key)
        if not issue_data:
            return False

        # Step 2: Parse firewall rules
        rules = self.parse_firewall_table(issue_data)
        if not rules:
            print("❌ No firewall rules found to analyze")
            return False

        # Step 3: Validate security standards
        violations = self.validate_security_standards(rules)

        # Step 4: Query Tufin (optional)
        tufin_results = self.query_tufin_rules(rules)

        # Step 5: Generate recommendations
        recommendations = self.generate_recommendations(violations, tufin_results)

        # Step 6: Display results
        print("\n" + "="*50)
        print("📊 ANALYSIS RESULTS")
        print("="*50)
        for rec in recommendations:
            print(rec)
        print("="*50)

        # Step 7: Post to Jira (unless dry run)
        if not dry_run:
            success = self.post_jira_comment(issue_key, recommendations)
            if not success:
                return False
        else:
            print("🔍 DRY RUN: Comment not posted to Jira")

        print("✅ Analysis completed successfully")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Simple Firewall Change Analyzer')
    parser.add_argument('--jira-issue', required=True, help='Jira issue key (e.g., FW-123)')
    parser.add_argument('--dry-run', action='store_true', help='Run analysis without posting to Jira')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = SimpleFirewallAnalyzer()

    # Validate configuration
    if not analyzer.validate_config():
        sys.exit(1)

    # Run analysis
    success = analyzer.analyze(args.jira_issue, args.dry_run)

    if success:
        print(f"🎉 Analysis completed for {args.jira_issue}")
        sys.exit(0)
    else:
        print(f"❌ Analysis failed for {args.jira_issue}")
        sys.exit(1)


if __name__ == '__main__':
    main()
