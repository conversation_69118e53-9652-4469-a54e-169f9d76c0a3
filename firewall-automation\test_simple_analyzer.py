#!/usr/bin/env python3
"""
Test Script for Simple Firewall Analyzer
========================================

This script provides comprehensive testing for the simple firewall analyzer
using mock data, allowing you to verify functionality without connecting to
actual Jira or Tufin systems.

Purpose:
    - Test all major components of the analyzer
    - Verify parsing logic with known data
    - Validate security checks against expected violations
    - Demonstrate the complete workflow
    - Provide examples of expected input/output formats

Usage:
    python test_simple_analyzer.py

Test Coverage:
    1. Firewall rule parsing from mock Jira issue
    2. Security standards validation
    3. Recommendation generation
    4. Complete workflow with mocked external calls

Benefits:
    - No external dependencies (Jira/Tufin not required)
    - Predictable test data for consistent results
    - Fast execution for development and CI/CD
    - Clear examples of expected data formats
"""

import os
import sys
from unittest.mock import patch, MagicMock
from simple_firewall_analyzer import SimpleFirewallAnalyzer, FirewallRule


def create_mock_jira_issue():
    """
    Create mock Jira issue data with firewall table for testing.

    This function creates a realistic Jira issue structure that includes
    multiple firewall rules with various security issues to test the
    analyzer's detection capabilities.

    Returns:
        dict: Mock Jira issue data structure matching real API response

    Test Rules Included:
        1. Rule with 'any' source and SSH protocol (HIGH + MEDIUM violations)
        2. Rule with specific source and HTTP protocol (no violations)
        3. Rule with wide port range and 'any' destination (MEDIUM + HIGH violations)

    This provides a good mix of violations to test the security validation logic.
    """
    return {
        "key": "FW-123",  # Mock issue key
        "fields": {
            "summary": "Firewall Change Request - Web Server Access",
            "description": """
            Please implement the following firewall changes:

            | Field | Value |
            |-------|-------|
            | Source | any |
            | Destination | ************* |
            | Port | 22 |
            | Protocol | SSH |
            | Action | allow |

            | Field | Value |
            |-------|-------|
            | Source | 10.0.0.0/24 |
            | Destination | ************* |
            | Port | 80 |
            | Protocol | HTTP |
            | Action | allow |

            | Field | Value |
            |-------|-------|
            | Source | **********/16 |
            | Destination | any |
            | Port | 1000-2000 |
            | Protocol | TCP |
            | Action | allow |
            """
        }
    }


def test_parsing():
    """
    Test the firewall rule parsing functionality.

    This test verifies that the analyzer can correctly extract firewall
    rules from a mock Jira issue description that contains table-formatted
    firewall rule data.

    Test Objectives:
        - Verify table parsing regex works correctly
        - Confirm all expected rules are extracted
        - Validate rule field mapping (source, destination, port, protocol)
        - Check handling of different table formats

    Returns:
        List[FirewallRule]: Parsed rules for use in other tests
    """
    print("🧪 Testing firewall rule parsing...")
    print("   Objective: Verify table parsing from Jira issue description")

    # Initialize analyzer (no external connections needed for parsing)
    analyzer = SimpleFirewallAnalyzer()

    # Get mock Jira issue data
    mock_issue = create_mock_jira_issue()
    print(f"   Input: Mock Jira issue '{mock_issue['key']}'")

    # Test the parsing functionality
    rules = analyzer.parse_firewall_table(mock_issue)

    # Verify and display results
    print(f"✅ Successfully parsed {len(rules)} rules:")
    for i, rule in enumerate(rules):
        print(f"  Rule {i+1}: {rule.source} -> {rule.destination}:{rule.port} ({rule.protocol})")

    # Validate expected number of rules
    expected_rules = 3  # Based on our mock data
    if len(rules) == expected_rules:
        print(f"✅ Correct number of rules parsed (expected {expected_rules})")
    else:
        print(f"⚠️  Expected {expected_rules} rules, got {len(rules)}")

    return rules


def test_security_validation():
    """
    Test the security standards validation functionality.

    This test creates firewall rules with known security issues and verifies
    that the analyzer correctly identifies and categorizes the violations.

    Test Objectives:
        - Verify detection of overly permissive sources ('any')
        - Verify detection of overly permissive destinations ('any')
        - Verify detection of risky protocols (SSH, RDP, etc.)
        - Verify detection of wide port ranges
        - Confirm correct severity assignment

    Test Rules:
        1. 'any' source with SSH - should trigger HIGH + MEDIUM violations
        2. Specific source with HTTP - should be clean
        3. Wide port range with 'any' destination - should trigger violations

    Returns:
        List[SecurityViolation]: Found violations for use in other tests
    """
    print("\n🧪 Testing security validation...")
    print("   Objective: Verify detection of security standard violations")

    # Initialize analyzer
    analyzer = SimpleFirewallAnalyzer()

    # Create test rules with known security issues
    test_rules = [
        # Rule 1: Multiple violations (any source + SSH protocol)
        FirewallRule(source="any", destination="*************", port="22", protocol="SSH"),

        # Rule 2: Clean rule (should have no violations)
        FirewallRule(source="10.0.0.0/24", destination="*************", port="80", protocol="HTTP"),

        # Rule 3: Wide port range + any destination
        FirewallRule(source="**********/16", destination="any", port="1000-2000", protocol="TCP")
    ]

    print(f"   Input: {len(test_rules)} test rules with known security issues")

    # Run security validation
    violations = analyzer.validate_security_standards(test_rules)

    # Display results with details
    print(f"✅ Security validation complete: found {len(violations)} violations")

    # Group violations by rule for clearer output
    violations_by_rule = {}
    for violation in violations:
        rule_num = violation.rule_index + 1
        if rule_num not in violations_by_rule:
            violations_by_rule[rule_num] = []
        violations_by_rule[rule_num].append(violation)

    # Display violations organized by rule
    for rule_num in sorted(violations_by_rule.keys()):
        rule_violations = violations_by_rule[rule_num]
        print(f"  Rule {rule_num} violations ({len(rule_violations)}):")
        for violation in rule_violations:
            print(f"    - {violation.severity}: {violation.issue}")

    # Verify expected violations
    expected_violations = 4  # Based on our test rules
    if len(violations) >= expected_violations:
        print(f"✅ Found expected violations (at least {expected_violations})")
    else:
        print(f"⚠️  Expected at least {expected_violations} violations, got {len(violations)}")

    return violations


def test_recommendations():
    """
    Test the recommendation generation functionality.

    This test verifies that the analyzer can combine security violations
    and Tufin analysis results into well-formatted recommendations that
    would be suitable for posting to Jira.

    Test Objectives:
        - Verify recommendation formatting and structure
        - Confirm integration of security violations
        - Confirm integration of Tufin analysis results
        - Validate markdown formatting for Jira
        - Check severity-based organization

    Test Data:
        - Creates rules with known violations
        - Provides mock Tufin analysis results
        - Combines both sources into recommendations

    Returns:
        List[str]: Generated recommendation strings
    """
    print("\n🧪 Testing recommendation generation...")
    print("   Objective: Verify formatting and integration of analysis results")

    # Initialize analyzer
    analyzer = SimpleFirewallAnalyzer()

    # Create test rules for recommendation generation
    test_rules = [
        # Rule with security violations
        FirewallRule(source="any", destination="*************", port="22", protocol="SSH"),
        # Clean rule for comparison
        FirewallRule(source="10.0.0.0/24", destination="*************", port="80", protocol="HTTP")
    ]

    print(f"   Input: {len(test_rules)} test rules")

    # Generate security violations
    violations = analyzer.validate_security_standards(test_rules)
    print(f"   Generated {len(violations)} security violations")

    # Create mock Tufin analysis results
    tufin_results = {
        "rule_1": {
            "existing_rules": [{"rule_id": "existing_ssh_rule"}],
            "conflicts": [{"type": "potential_overlap", "severity": "medium", "message": "May overlap with existing SSH rule"}],
            "recommendations": ["Consider creating specific rule for SSH access", "Enable logging for monitoring"]
        },
        "rule_2": {
            "existing_rules": [],
            "conflicts": [],
            "recommendations": ["HTTP rule looks acceptable", "Consider HTTPS instead of HTTP"]
        }
    }

    print(f"   Mock Tufin results for {len(tufin_results)} rules")

    # Generate recommendations
    recommendations = analyzer.generate_recommendations(violations, tufin_results)

    # Display generated recommendations
    print(f"✅ Generated {len(recommendations)} recommendation lines:")
    print("   Preview of recommendations:")
    for i, rec in enumerate(recommendations[:10]):  # Show first 10 lines
        print(f"     {i+1:2d}. {rec}")

    if len(recommendations) > 10:
        print(f"     ... and {len(recommendations) - 10} more lines")

    # Validate recommendation structure
    has_security_section = any("Security Violations" in rec for rec in recommendations)
    has_tufin_section = any("Tufin Analysis" in rec for rec in recommendations)

    if has_security_section:
        print("✅ Security violations section included")
    else:
        print("⚠️  Security violations section missing")

    if has_tufin_section:
        print("✅ Tufin analysis section included")
    else:
        print("⚠️  Tufin analysis section missing")

    return recommendations


def test_full_workflow():
    """
    Test the complete end-to-end workflow with mocked external calls.

    This test simulates the entire analyzer workflow from start to finish,
    using mocked external API calls to avoid dependencies on actual Jira
    and Tufin systems.

    Test Objectives:
        - Verify complete workflow integration
        - Test error handling and recovery
        - Confirm proper sequencing of operations
        - Validate dry-run functionality
        - Ensure all components work together

    Mocking Strategy:
        - Mock Jira API calls to return test data
        - Mock Jira comment posting to avoid external writes
        - Use real internal logic for parsing and validation
        - Simulate successful external API responses

    Workflow Steps Tested:
        1. Configuration validation
        2. Jira issue retrieval (mocked)
        3. Firewall rule parsing
        4. Security validation
        5. Tufin analysis (skipped due to no config)
        6. Recommendation generation
        7. Result display
        8. Jira comment posting (mocked)

    Returns:
        bool: True if workflow completed successfully, False otherwise
    """
    print("\n🧪 Testing full workflow with mocked external calls...")
    print("   Objective: Verify complete end-to-end integration")

    # Set up mock environment variables for testing
    # These simulate a real environment without requiring actual credentials
    print("   Setting up mock environment variables...")
    os.environ['JIRA_URL'] = 'https://mock-jira.example.com'
    os.environ['JIRA_USERNAME'] = 'test-user'
    os.environ['JIRA_API_TOKEN'] = 'mock-token'

    # Note: Intentionally not setting Tufin variables to test optional functionality

    # Initialize analyzer with mock configuration
    analyzer = SimpleFirewallAnalyzer()
    print("   Analyzer initialized with mock configuration")

    # Use Python's unittest.mock to replace external API calls
    # This allows us to test internal logic without external dependencies
    with patch.object(analyzer, 'get_jira_issue') as mock_jira, \
         patch.object(analyzer, 'post_jira_comment') as mock_comment:

        print("   Setting up mock API responses...")

        # Configure mock Jira issue retrieval to return our test data
        mock_jira.return_value = create_mock_jira_issue()
        print("     - Jira issue retrieval will return mock data")

        # Configure mock comment posting to simulate success
        mock_comment.return_value = True
        print("     - Jira comment posting will simulate success")

        # Run the complete analysis workflow
        print("   Executing complete workflow...")
        test_issue_key = "FW-123"
        success = analyzer.analyze(test_issue_key, dry_run=True)

        # Verify that mocked methods were called as expected
        print("   Verifying mock interactions...")

        # Check that Jira issue was retrieved
        mock_jira.assert_called_once_with(test_issue_key)
        print("     ✅ Jira issue retrieval was called correctly")

        # In dry-run mode, comment posting should NOT be called
        mock_comment.assert_not_called()
        print("     ✅ Jira comment posting was skipped (dry-run mode)")

        # Evaluate overall success
        if success:
            print("✅ Full workflow test passed!")
            print("   All components integrated successfully")
            print("   Mock external calls handled correctly")
            print("   Dry-run mode functioned as expected")
        else:
            print("❌ Full workflow test failed!")
            print("   Check error messages above for details")

        return success


def main():
    """
    Execute all test functions and provide comprehensive results.

    This function orchestrates the complete test suite for the simple
    firewall analyzer, running individual component tests and integration
    tests to verify all functionality works correctly.

    Test Execution Order:
        1. Firewall rule parsing test
        2. Security validation test
        3. Recommendation generation test
        4. Full workflow integration test

    Success Criteria:
        - All individual component tests must pass
        - Full workflow test must complete successfully
        - No exceptions during test execution

    Returns:
        int: Exit code (0 for success, 1 for failure)

    Output:
        - Detailed test results for each component
        - Summary statistics
        - Pass/fail status for each test
        - Overall test suite result
    """
    print("🚀 Starting Simple Firewall Analyzer Tests")
    print("=" * 60)
    print("This test suite verifies all components of the analyzer")
    print("using mock data to avoid external dependencies.")
    print("=" * 60)

    # Track test results for final summary
    test_results = {}

    try:
        # TEST 1: Firewall Rule Parsing
        print("\n" + "🔍 TEST 1: FIREWALL RULE PARSING" + "\n" + "-" * 40)
        rules = test_parsing()
        test_results['parsing'] = {
            'passed': rules is not None and len(rules) > 0,
            'count': len(rules) if rules else 0,
            'description': 'Parse firewall rules from Jira issue description'
        }

        # TEST 2: Security Standards Validation
        print("\n" + "🔒 TEST 2: SECURITY VALIDATION" + "\n" + "-" * 40)
        violations = test_security_validation()
        test_results['validation'] = {
            'passed': violations is not None,
            'count': len(violations) if violations else 0,
            'description': 'Validate rules against security standards'
        }

        # TEST 3: Recommendation Generation
        print("\n" + "📝 TEST 3: RECOMMENDATION GENERATION" + "\n" + "-" * 40)
        recommendations = test_recommendations()
        test_results['recommendations'] = {
            'passed': recommendations is not None and len(recommendations) > 0,
            'count': len(recommendations) if recommendations else 0,
            'description': 'Generate formatted recommendations'
        }

        # TEST 4: Full Workflow Integration
        print("\n" + "🔄 TEST 4: FULL WORKFLOW INTEGRATION" + "\n" + "-" * 40)
        workflow_success = test_full_workflow()
        test_results['workflow'] = {
            'passed': workflow_success,
            'count': 1 if workflow_success else 0,
            'description': 'Complete end-to-end workflow'
        }

        # GENERATE TEST SUMMARY
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)

        # Display individual test results
        all_passed = True
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result['passed'] else "❌ FAILED"
            print(f"{status} | {test_name.upper():20} | {result['description']}")
            if result['count'] > 0:
                print(f"       | {'':20} | Count: {result['count']}")
            if not result['passed']:
                all_passed = False

        print("-" * 60)

        # Display summary statistics
        print("📈 STATISTICS:")
        print(f"   Rules Parsed: {test_results['parsing']['count']}")
        print(f"   Violations Found: {test_results['validation']['count']}")
        print(f"   Recommendations Generated: {test_results['recommendations']['count']}")
        print(f"   Workflow Status: {'SUCCESS' if test_results['workflow']['passed'] else 'FAILED'}")

        print("-" * 60)

        # Final result
        if all_passed:
            print("🎉 ALL TESTS PASSED!")
            print("   The simple firewall analyzer is working correctly.")
            print("   You can now use it with confidence on real Jira issues.")
            print("\n💡 Next Steps:")
            print("   1. Set up your environment variables (JIRA_URL, etc.)")
            print("   2. Run: python simple_firewall_analyzer.py --jira-issue YOUR-ISSUE --dry-run")
            print("   3. Review the output and remove --dry-run when ready")
            return 0
        else:
            print("❌ SOME TESTS FAILED!")
            print("   Please review the error messages above.")
            print("   Fix any issues before using the analyzer.")
            return 1

    except Exception as e:
        print(f"\n💥 TEST EXECUTION FAILED: {e}")
        print("   This indicates a serious issue with the test setup or analyzer code.")
        print("   Please check the error details above and fix before proceeding.")
        import traceback
        traceback.print_exc()
        return 1


# Entry point when script is run directly
if __name__ == '__main__':
    sys.exit(main())
