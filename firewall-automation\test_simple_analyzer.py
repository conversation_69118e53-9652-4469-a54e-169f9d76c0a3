#!/usr/bin/env python3
"""
Test script for the simple firewall analyzer using mock data.
This allows testing without connecting to actual Jira/Tufin systems.
"""

import os
import sys
from unittest.mock import patch, MagicMock
from simple_firewall_analyzer import SimpleFirewallAnalyzer, FirewallRule


def create_mock_jira_issue():
    """Create mock Jira issue data with firewall table."""
    return {
        "key": "FW-123",
        "fields": {
            "summary": "Firewall Change Request - Web Server Access",
            "description": """
            Please implement the following firewall changes:
            
            | Field | Value |
            |-------|-------|
            | Source | any |
            | Destination | ************* |
            | Port | 22 |
            | Protocol | SSH |
            | Action | allow |
            
            | Field | Value |
            |-------|-------|
            | Source | 10.0.0.0/24 |
            | Destination | ************* |
            | Port | 80 |
            | Protocol | HTTP |
            | Action | allow |
            
            | Field | Value |
            |-------|-------|
            | Source | **********/16 |
            | Destination | any |
            | Port | 1000-2000 |
            | Protocol | TCP |
            | Action | allow |
            """
        }
    }


def test_parsing():
    """Test firewall rule parsing."""
    print("🧪 Testing firewall rule parsing...")
    
    analyzer = SimpleFirewallAnalyzer()
    mock_issue = create_mock_jira_issue()
    
    rules = analyzer.parse_firewall_table(mock_issue)
    
    print(f"✅ Parsed {len(rules)} rules:")
    for i, rule in enumerate(rules):
        print(f"  Rule {i+1}: {rule.source} -> {rule.destination}:{rule.port} ({rule.protocol})")
    
    return rules


def test_security_validation():
    """Test security validation."""
    print("\n🧪 Testing security validation...")
    
    analyzer = SimpleFirewallAnalyzer()
    
    # Create test rules with known violations
    test_rules = [
        FirewallRule(source="any", destination="*************", port="22", protocol="SSH"),
        FirewallRule(source="10.0.0.0/24", destination="*************", port="80", protocol="HTTP"),
        FirewallRule(source="**********/16", destination="any", port="1000-2000", protocol="TCP")
    ]
    
    violations = analyzer.validate_security_standards(test_rules)
    
    print(f"✅ Found {len(violations)} violations:")
    for violation in violations:
        print(f"  Rule {violation.rule_index + 1} - {violation.severity}: {violation.issue}")
    
    return violations


def test_recommendations():
    """Test recommendation generation."""
    print("\n🧪 Testing recommendation generation...")
    
    analyzer = SimpleFirewallAnalyzer()
    
    # Use violations from previous test
    test_rules = [
        FirewallRule(source="any", destination="*************", port="22", protocol="SSH"),
        FirewallRule(source="10.0.0.0/24", destination="*************", port="80", protocol="HTTP")
    ]
    
    violations = analyzer.validate_security_standards(test_rules)
    tufin_results = {
        "rule_1": {"recommendations": ["Consider creating specific rule for SSH access"]},
        "rule_2": {"recommendations": ["HTTP rule looks acceptable"]}
    }
    
    recommendations = analyzer.generate_recommendations(violations, tufin_results)
    
    print("✅ Generated recommendations:")
    for rec in recommendations:
        print(f"  {rec}")
    
    return recommendations


def test_full_workflow():
    """Test the complete workflow with mocked external calls."""
    print("\n🧪 Testing full workflow with mocked external calls...")
    
    # Set up mock environment variables
    os.environ['JIRA_URL'] = 'https://mock-jira.example.com'
    os.environ['JIRA_USERNAME'] = 'test-user'
    os.environ['JIRA_API_TOKEN'] = 'mock-token'
    
    analyzer = SimpleFirewallAnalyzer()
    
    # Mock the external API calls
    with patch.object(analyzer, 'get_jira_issue') as mock_jira, \
         patch.object(analyzer, 'post_jira_comment') as mock_comment:
        
        # Set up mock responses
        mock_jira.return_value = create_mock_jira_issue()
        mock_comment.return_value = True
        
        # Run analysis
        success = analyzer.analyze("FW-123", dry_run=True)
        
        if success:
            print("✅ Full workflow test passed!")
        else:
            print("❌ Full workflow test failed!")
        
        return success


def main():
    """Run all tests."""
    print("🚀 Starting Simple Firewall Analyzer Tests")
    print("=" * 50)
    
    try:
        # Test individual components
        rules = test_parsing()
        violations = test_security_validation()
        recommendations = test_recommendations()
        
        # Test full workflow
        workflow_success = test_full_workflow()
        
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        print(f"✅ Rules parsed: {len(rules) if rules else 0}")
        print(f"⚠️  Violations found: {len(violations) if violations else 0}")
        print(f"📝 Recommendations: {len(recommendations) if recommendations else 0}")
        print(f"🔄 Full workflow: {'PASSED' if workflow_success else 'FAILED'}")
        
        if workflow_success:
            print("\n🎉 All tests passed! The simple analyzer is working correctly.")
            return 0
        else:
            print("\n❌ Some tests failed. Please check the output above.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
